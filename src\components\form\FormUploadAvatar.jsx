import { Button, Input, cn } from "@heroui/react";
import { Add } from "iconsax-reactjs";
import PropTypes from "prop-types";
import { useCallback, useRef } from "react";
import { useDropzone } from "react-dropzone";
import { useController } from "react-hook-form";
/**
 * @param {FormUploadAvatarProps} props
 */
const FormUploadAvatar = (props) => {
  const { control, name, classNames = {} } = props;

  const fileInputRef = useRef(null);

  const {
    field: { onChange, value },
  } = useController({ name, control });

  const onDrop = useCallback(
    (acceptedFiles) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        onChange(acceptedFiles[0]);
      }
    },
    [onChange],
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: "image/jpeg, image/png",
    multiple: false,
    noClick: true,
  });

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div
      {...getRootProps()}
      className={cn(
        "group relative aspect-square size-full max-h-32 max-w-32 rounded-full ring-2 ring-background-300 ring-offset-4",
        classNames?.wrapper,
      )}
    >
      <Input {...getInputProps()} className="hidden" ref={fileInputRef} />

      <img
        className={cn(
          "size-full rounded-full object-cover",
          classNames?.avatar,
        )}
        src={value ? URL.createObjectURL(value) : "/images/user.png"}
      />

      <Button
        isIconOnly
        radius="full"
        color="primary"
        onPress={handleUploadClick}
        className={cn(
          "absolute hover:!opacity-100 -bottom-3 border-background border-2 start-0",
          classNames?.button,
        )}
      >
        <Add className="size-9" />
      </Button>
    </div>
  );
};

FormUploadAvatar.propTypes = {
  control: PropTypes.object.isRequired,
  name: PropTypes.string.isRequired,
  classNames: PropTypes.shape({
    avatar: PropTypes.string,
    wrapper: PropTypes.string,
    button: PropTypes.string,
  }),
};

export default FormUploadAvatar;
