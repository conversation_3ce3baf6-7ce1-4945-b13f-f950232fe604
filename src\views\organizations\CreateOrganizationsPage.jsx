import { AutocompleteItem, Avatar, Button, Radio } from "@heroui/react";
import { Add, Edit, Trash } from "iconsax-reactjs";
import { useForm } from "react-hook-form";
import FormAutoComplete from "../../components/form/FormAutoComplete";
import FormDatePicker from "../../components/form/FormDatePicker";
import FormInput from "../../components/form/FormInput";
import FormRadioGroup from "../../components/form/FormRadioGroup";
import FormRichText from "../../components/form/FormRichText/FormRichText";
import FormSwitch from "../../components/form/FormSwitch";
import FormUpload from "../../components/form/FormUpload";
import FormUploadAvatar from "../../components/form/FormUploadAvatar";
import Icon from "../../components/icon/Icon";
import PageWrapper from "../../components/layout/PageWrapper";

const users = [
  {
    id: 1,
    name: "<PERSON>",
    role: "CEO",
    phone: "Management",
    status: "active",
    age: "29",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/1.png",
    email: "<EMAIL>",
  },
  {
    id: 2,
    name: "Zoey Lang",
    role: "Tech Lead",
    phone: "Development",
    status: "paused",
    age: "25",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/1.png",
    email: "<EMAIL>",
  },
  {
    id: 3,
    name: "Jane Fisher",
    role: "Sr. Dev",
    phone: "Development",
    status: "active",
    age: "22",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/2.png",
    email: "<EMAIL>",
  },
  {
    id: 4,
    name: "William Howard",
    role: "C.M.",
    phone: "Marketing",
    status: "vacation",
    age: "28",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/2.png",
    email: "<EMAIL>",
  },
  {
    id: 5,
    name: "Kristen Copper",
    role: "S. Manager",
    phone: "Sales",
    status: "active",
    age: "24",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/3.png",
    email: "<EMAIL>",
  },
  {
    id: 6,
    name: "Brian Kim",
    role: "P. Manager",
    phone: "Management",
    age: "29",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/3.png",
    email: "<EMAIL>",
    status: "active",
  },
  {
    id: 7,
    name: "Michael Hunt",
    role: "Designer",
    phone: "Design",
    status: "paused",
    age: "27",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/4.png",
    email: "<EMAIL>",
  },
  {
    id: 8,
    name: "Samantha Brooks",
    role: "HR Manager",
    phone: "HR",
    status: "active",
    age: "31",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/4.png",
    email: "<EMAIL>",
  },
  {
    id: 9,
    name: "Frank Harrison",
    role: "F. Manager",
    phone: "Finance",
    status: "vacation",
    age: "33",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/5.png",
    email: "<EMAIL>",
  },
  {
    id: 10,
    name: "Emma Adams",
    role: "Ops Manager",
    phone: "Operations",
    status: "active",
    age: "35",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/5.png",
    email: "<EMAIL>",
  },
  {
    id: 11,
    name: "Brandon Stevens",
    role: "Jr. Dev",
    phone: "Development",
    status: "active",
    age: "22",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/7.png",
    email: "<EMAIL>",
  },
  {
    id: 12,
    name: "Megan Richards",
    role: "P. Manager",
    phone: "Product",
    status: "paused",
    age: "28",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/7.png",
    email: "<EMAIL>",
  },
  {
    id: 13,
    name: "Oliver Scott",
    role: "S. Manager",
    phone: "Security",
    status: "active",
    age: "37",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/8.png",
    email: "<EMAIL>",
  },
  {
    id: 14,
    name: "Grace Allen",
    role: "M. Specialist",
    phone: "Marketing",
    status: "active",
    age: "30",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/8.png",
    email: "<EMAIL>",
  },
  {
    id: 15,
    name: "Noah Carter",
    role: "IT Specialist",
    phone: "I. Technology",
    status: "paused",
    age: "31",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/9.png",
    email: "<EMAIL>",
  },
  {
    id: 16,
    name: "Ava Perez",
    role: "Manager",
    phone: "Sales",
    status: "active",
    age: "29",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/9.png",
    email: "<EMAIL>",
  },
  {
    id: 17,
    name: "Liam Johnson",
    role: "Data Analyst",
    phone: "Analysis",
    status: "active",
    age: "28",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/11.png",
    email: "<EMAIL>",
  },
  {
    id: 18,
    name: "Sophia Taylor",
    role: "QA Analyst",
    phone: "Testing",
    status: "active",
    age: "27",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/11.png",
    email: "<EMAIL>",
  },
  {
    id: 19,
    name: "Lucas Harris",
    role: "Administrator",
    phone: "Information Technology",
    status: "paused",
    age: "32",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/12.png",
    email: "<EMAIL>",
  },
  {
    id: 20,
    name: "Mia Robinson",
    role: "Coordinator",
    phone: "Operations",
    status: "active",
    age: "26",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/12.png",
    email: "<EMAIL>",
  },
];

const CreateOrganizationsPage = () => {
  const { control, watch, setValue } = useForm({
    defaultValues: {
      text: "",
      users: [],
    },
  });

  return (
    <PageWrapper hasTitle={false}>
      <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
        <FormUploadAvatar
          name="image"
          control={control}
          classNames={{
            wrapper: "max-h-28 max-w-28",
          }}
        />

        <p className="font-medium mt-2">اطلاعات سازمان</p>

        <div className="flex items-center gap-4">
          <FormInput
            control={control}
            name="name"
            type="text"
            inputProps={{
              classNames: {
                base: "max-w-sm",
                inputWrapper:
                  "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                input: "text-sm",
              },
              size: "lg",
              radius: "full",
              placeholder: "نام سازمان",
              startContent: <Edit className="size-6 text-primary" />,
            }}
          />
          <FormDatePicker
            control={control}
            name="date"
            size="lg"
            radius="full"
            startContent={
              <Icon className={"text-primary size-7"} name={"accept-note"} />
            }
            classNames={{
              base: "max-w-sm",
              inputWrapper:
                "shadow-sm hover:!bg-background-100 border hover:border-foreground-200 transition-colors border-foreground-100",
              input: "text-sm ",
            }}
          />
        </div>

        <p className="font-medium mt-2">ویدیوی مرتبط با سازمان</p>

        <FormUpload control={control} name="video" />

        <p className="font-medium mt-2">توضیحات مرتبط با سازمان</p>
        <FormRichText control={control} name="description" />

        <FormSwitch
          control={control}
          className="ltr"
          name="active"
          label="وضعیت سازمان"
        />
      </div>

      <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
        <div className="flex w-full  items-center gap-4">
          <FormAutoComplete
            control={control}
            name="users"
            items={users}
            isFilterable={true}
            renderItem={(item) => (
              <AutocompleteItem
                classNames={{
                  selectedIcon: "hidden",
                  base: "hover:!bg-foreground-100/80 data-[hover=true]:bg-foreground-100/80",
                }}
                key={item.id}
                textValue={item.name}
              >
                <div className="flex justify-between items-center">
                  <div className="flex gap-2 items-center">
                    <Avatar
                      alt={item.name}
                      className="flex-shrink-0"
                      src={item.avatar}
                    />
                    <div className="flex flex-col">
                      <span className="text-small ">{item.name}</span>
                      <span className="text-tiny text-default-400">
                        {item.phone}
                      </span>
                    </div>
                  </div>
                  <Button radius="full" size="sm" color="primary">
                    افزودن
                  </Button>
                </div>
              </AutocompleteItem>
            )}
            autoCompleteProps={{
              fullWidth: true,
              isClearable: false,
              multiple: true,
              listboxProps: {
                emptyContent: "چیزی پیدا نشد",
              },
              // isLoading: true,
              inputProps: {
                classNames: {
                  inputWrapper:
                    "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  input: "text-sm",
                },
                size: "lg",
                radius: "full",
                placeholder: "جستجوی ادمین با استفاده از شماره موبایل",
              },
            }}
          />
          <Button
            type="button"
            color="primary"
            radius="full"
            className="px-6 h-12 shrink-0"
            startContent={<Add className="size-6" />}
          >
            ایجاد ادمین جدید
          </Button>
        </div>

        <ul className="space-y-4">
          {watch("users")?.map((userId) => {
            const user = users.find((u) => u.id.toString() === userId);
            return user ? (
              <li
                key={user.id}
                className="flex items-center rounded-3xl gap-4 p-6 border border-foreground-100 shadow-sm"
              >
                <Avatar
                  alt={user.name}
                  className="flex-shrink-0"
                  src={user.avatar}
                  size="lg"
                />

                <div className="flex items-center font-medium gap-2">
                  <p className="text-foreground-400">نام و نام خانوادگی: </p>
                  <p>{user.name}</p>
                </div>

                <div className="flex items-center font-medium gap-2">
                  <p className="text-foreground-400">شماره تماس: </p>
                  <p>{user.phone}</p>
                </div>

                <Button
                  className="ms-auto"
                  isIconOnly
                  radius="full"
                  color="danger"
                  variant="light"
                  onPress={() => {
                    setValue(
                      "users",
                      watch("users").filter((u) => u !== userId),
                    );
                  }}
                >
                  <Trash />
                </Button>
              </li>
            ) : null;
          })}
        </ul>

        <p className="font-medium mt-2">نقش</p>

        <div className="flex items-end gap-3">
          <FormInput
            control={control}
            name="rolePersianName"
            inputProps={{
              label: "عنوان نقش",
              labelPlacement: "outside",
              radius: "full",
              size: "lg",
              classNames: {
                base: "max-w-xs",
                inputWrapper:
                  "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                input: "text-sm",
              },
            }}
          />
          <FormInput
            control={control}
            name="roleLatinName"
            inputProps={{
              label: "نام لاتین",
              labelPlacement: "outside",
              radius: "full",
              size: "lg",
              classNames: {
                base: "max-w-xs",
                inputWrapper:
                  "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                input: "text-sm",
              },
            }}
          />
        </div>

        <p className="font-medium mt-2">کاربران</p>

        <div className="flex items-center gap-3">
          <FormRadioGroup
            control={control}
            name="roleUsers"
            radioGroupProps={{
              orientation: "horizontal",
            }}
            items={[
              {
                key: "test1",
                value: "test1",
              },
              {
                key: "test2",
                value: "test2",
              },
              {
                key: "test3",
                value: "test3",
              },
            ]}
            renderItem={(item) => {
              return (
                <Radio value={item.value} key={item.key}>
                  {item.value}
                </Radio>
              );
            }}
          />
        </div>
      </div>
    </PageWrapper>
  );
};

export default CreateOrganizationsPage;
