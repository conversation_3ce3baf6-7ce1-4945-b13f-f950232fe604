import { DatePicker, DateRangePicker, cn } from "@heroui/react";
import PropTypes from "prop-types";
import { useController } from "react-hook-form";

/**
 * @param {FormDatePickerProps} props
 */
const FormDatePicker = (props) => {
  const {
    name,
    control,
    className,
    classNames = {},
    isRange,
    ...other
  } = props;

  const {
    field: { onChange, ...field },
    fieldState,
  } = useController({ name, control });

  const defaultProps = {
    labelPlacement: "outside",
    className: cn("z-0", className),
    ...other,
  };

  if (isRange) {
    return (
      <DateRangePicker
        {...field}
        showMonthAndYearPickers
        onChange={(date) => {
          onChange(date);
        }}
        isInvalid={!!fieldState.error}
        errorMessage={fieldState.error?.message}
        classNames={{
          segment: cn("ltr", classNames?.segment),
          input: cn("ltr justify-end", classNames?.input),
          inputWrapper: cn(
            [
              !fieldState.error ? "bg-background" : "",
              "hover:bg-background-100",
              "dark:hover:bg-background-200/90",
            ],
            classNames?.inputWrapper,
          ),
          ...classNames,
        }}
        {...defaultProps}
      />
    );
  }
  return (
    <DatePicker
      {...field}
      showMonthAndYearPickers
      classNames={{
        segment: cn("ltr", classNames?.segment),
        input: cn("ltr justify-end", classNames?.input),
        inputWrapper: cn(
          [
            !fieldState.error ? "bg-background" : "",
            "hover:bg-background-100",
            "dark:hover:bg-background-200/90",
          ],
          classNames?.inputWrapper,
        ),
        ...(({ segment, input, inputWrapper, ...rest }) => rest)(classNames),
      }}
      onChange={(date) => {
        onChange(date);
      }}
      isInvalid={!!fieldState.error}
      errorMessage={fieldState.error?.message}
      {...defaultProps}
    />
  );
};

FormDatePicker.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  classNames: PropTypes.shape({
    segment: PropTypes.string,
    input: PropTypes.string,
    inputWrapper: PropTypes.string,
    base: PropTypes.string,
  }),
  isRange: PropTypes.bool,
  className: PropTypes.string,
  color: PropTypes.string,
  label: PropTypes.string,
  variant: PropTypes.oneOf(["faded", "bordered", "flat", "underline"]),
  labelPlacement: PropTypes.oneOf(["outside", "inside", "outside-left"]),
  radius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
  size: PropTypes.oneOf(["sm", "md", "lg"]),
};

export default FormDatePicker;
