import { Button } from "@heroui/react";
import { Transmit } from "iconsax-reactjs";
import { useNavigate } from "react-router";
import api from "../../api";
import PageWrapper from "../../components/layout/PageWrapper";
import ListData from "../../components/table/ListData";

const StudentsPage = () => {
  const navigate = useNavigate();

  const columns = [
    {
      id: "1",
      title: "نام و نام خانوادگی",
      type: "user",
      field: (item) => ({
        name: `${item.first_name} ${item.last_name}`,
        avatar: item.avatar,
        description: item.mobile,
      }),
    },
    { id: "2", title: "مدرس فعلی", field: "teacher" },
    { id: "3", title: "سطح فعلی", field: "current_level" },
    { id: "4", title: "جلسات برگزار شده", field: "sessions_complete" },
    { id: "5", title: "ساعت آموزشی", field: "sessions_time" },
    { id: "6", title: "درصد حضور به غیبت", field: "attendance_percentage" },
    {
      id: "7",
      title: "فعال/غیرفعال",
      field: "active",
      type: "status",
    },
    {
      id: "8",
      title: "عملکرد آموزشی",
      type: "custom",
      exportable: false,
      field: (item) => (
        <Button
          type="button"
          color="primary"
          variant="bordered"
          onPress={() => navigate(`/students/${item.id}/reports`)}
          className="border-transparent border-1 hover:border-primary"
          size="sm"
          endContent={<Transmit className="size-3.5" />}
        >
          <span className="mt-1 text-sm">مشاهده</span>
        </Button>
      ),
    },
  ];

  return (
    <PageWrapper>
      <ListData
        features={{
          export: { show: true },
          pagination: { show: true },
          table: { color: "primary", isStriped: true },
          filter: {
            show: true,
            className: "flex-wrap lg:flex-nowrap gap-4",
            items: [
              {
                name: "username",
                type: "search",
                props: {
                  radius: "full",
                  placeholder: "جستجوی زبان‌آموز بر اساس نام یا شماره موبایل",
                  classNames: {
                    base: "w-full",
                    inputWrapper:
                      "!bg-background hover:!bg-background-100 min-h-11 shadow-sm border border-foreground-100 hover:border-foreground-200 transition-colors",
                  },
                },
              },
              {
                name: "active",
                type: "select",
                props: {
                  label: "وضعیت",
                  radius: "full",
                  items: [
                    { label: "همه", key: "null" },
                    { label: "فعال", key: "true" },
                    { label: "غیرفعال", key: "false" },
                  ],
                  placeholder: "وضعیت",
                  labelPlacement: "outside-left",
                  defaultSelectedKeys: "null",
                  classNames: {
                    base: " w-full max-w-xs",
                    trigger:
                      "!bg-background min-h-11 hover:!bg-background-100 !outline-none shadow-sm border border-foreground-100 hover:border-foreground-200 transition-colors",
                  },
                },
              },
              {
                name: "level",
                type: "select",
                props: {
                  label: "سطح",
                  radius: "full",
                  items: [
                    { label: "همه", key: "null" },
                    { label: "A1-", key: "A1-" },
                    { label: "A1", key: "A1" },
                    { label: "A2", key: "A2" },
                    { label: "A2+", key: "A2+" },
                    { label: "B1", key: "B1" },
                    { label: "B1+", key: "B1+" },
                    { label: "B2", key: "B2" },
                    { label: "B2+", key: "B2+" },
                    { label: "C1", key: "C1" },
                    { label: "C2", key: "C2" },
                  ],
                  placeholder: "انتخاب سطح",
                  labelPlacement: "outside-left",
                  defaultSelectedKeys: "null",
                  classNames: {
                    base: " w-full max-w-xs",
                    trigger:
                      "!bg-background min-h-11 hover:!bg-background-100 !outline-none shadow-sm border border-foreground-100 hover:border-foreground-200 transition-colors",
                  },
                },
              },
              {
                name: "date",
                type: "date",
                props: {
                  label: "انتخاب تاریخ",
                  labelPlacement: "outside-left",
                  isRange: true,
                  radius: "full",
                  selectorButtonPlacement: "start",
                  classNames: {
                    base: " w-full max-w-xs",
                    inputWrapper:
                      "min-h-11 !outline-none shadow-sm hover:!bg-background-100 border border-foreground-100 hover:border-foreground-200 transition-colors",
                  },
                  calendarProps: {
                    color: "secondary",
                  },
                },
              },
            ],
          },
        }}
        queryKey={api.Students.list}
        columns={columns}
      />
    </PageWrapper>
  );
};

export default StudentsPage;
