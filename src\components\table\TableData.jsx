import {
  Spinner,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import PropTypes from "prop-types";
import RenderCell from "./RenderCell";

/**
 * @param {TableDataProps} props
 */
export default function TableData(props) {
  const {
    columns,
    emptyContent = "موردی یافت نشد",
    data,
    pagination,
    selectionMode = "single",
    readOnly = false,
    onSelectionChange,
    selectedKeys,
    isLoading,
    ...other
  } = props;

  const selectedAttrs = {
    selectedKeys,
    selectionMode,
    onSelectionChange,
  };

  return (
    <Table
      sortDescriptor
      {...(!readOnly ? selectedAttrs : {})}
      aria-label="Table Custom"
      classNames={{
        wrapper: "p-0 rounded-small custom-scrollbar",
        th: [
          "bg-secondary",
          "last:rounded-e-none first:rounded-s-none first:rounded-tr-lg last:rounded-tl-lg",
          "text-background",
          "py-4 px-8 ",
        ],
        td: [
          "group-data-[first=true]/tr:first:before:rounded-none",
          "group-data-[first=true]/tr:last:before:rounded-none",
          "group-data-[middle=true]/tr:before:rounded-none",
          "group-data-[last=true]/tr:first:before:rounded-none",
          "group-data-[last=true]/tr:last:before:rounded-none",
          "px-8 pt-4 text-center first:text-start",
        ],
      }}
      className={"grid grid-cols-1"}
      {...other}
    >
      <TableHeader>
        {columns.map((column) => (
          <TableColumn
            className="text-center"
            {...(column.attrs ?? {})}
            key={`col${column.id}`}
          >
            {column.title}
          </TableColumn>
        ))}
      </TableHeader>
      <TableBody
        items={data}
        emptyContent={emptyContent}
        isLoading={isLoading}
        loadingContent={<Spinner />}
      >
        {data.map((item, index) => {
          return (
            <TableRow key={item.id}>
              {columns.map((col) => (
                <TableCell {...(col.attrs ?? {})} key={`col${col.id}`}>
                  <RenderCell
                    item={item}
                    column={col}
                    index={index}
                    pagination={pagination}
                  />
                </TableCell>
              ))}
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
}

TableData.propTypes = {
  columns: PropTypes.arrayOf(PropTypes.object),
  emptyContent: PropTypes.node,
  data: PropTypes.array,
  pagination: PropTypes.object,
  selectionMode: PropTypes.oneOf(["none", "multiple", "single"]),
  readOnly: PropTypes.bool,
  onSelectionChange: PropTypes.func,
  selectedKeys: PropTypes.object,
  isLoading: PropTypes.bool,
};
